#include "detection_config.h"
#include "simple_yaml.h"
#include <iostream>
#include <fstream>

bool ConfigLoader::loadFromFile(const std::string& filename, Config& config) {
    auto yaml = SimpleYAML::loadFromFile(filename);
    if (!yaml) {
        std::cerr << "Failed to load config file: " << filename << std::endl;
        return false;
    }

    try {
        // 加载流配置
        if (auto stream = yaml->operator[]("stream")) {
            config.stream.input_stream = stream->operator[]("input_stream")->asString();
            config.stream.output_stream = stream->operator[]("output_stream")->asString();
            config.stream.output_width = stream->operator[]("output_width")->asInt(1920);
            config.stream.output_height = stream->operator[]("output_height")->asInt(1080);
            config.stream.fps = stream->operator[]("fps")->asDouble(30.0);
            config.stream.maintain_aspect_ratio = stream->operator[]("maintain_aspect_ratio")->asBool(true);

            // 加载编码配置
            if (auto encoding = stream->operator[]("encoding")) {
                config.stream.encoding.keyframe_interval = encoding->operator[]("keyframe_interval")->asDouble(2.0);
                config.stream.encoding.force_keyframes = encoding->operator[]("force_keyframes")->asBool(false);
                config.stream.encoding.preset = encoding->operator[]("preset")->asString("medium");
                config.stream.encoding.profile = encoding->operator[]("profile")->asString("main");
                config.stream.encoding.level = encoding->operator[]("level")->asString("4.0");
                config.stream.encoding.zero_latency = encoding->operator[]("zero_latency")->asBool(false);
                config.stream.encoding.refs = encoding->operator[]("refs")->asInt(3);
                config.stream.encoding.bframes = encoding->operator[]("bframes")->asInt(3);
            }
        }

        // 加载处理器配置
        if (auto processor = yaml->operator[]("processor")) {
            config.processor.num_threads = processor->operator[]("num_threads")->asInt(4);
            config.processor.input_queue_size = processor->operator[]("input_queue_size")->asInt(10);
            config.processor.output_queue_size = processor->operator[]("output_queue_size")->asInt(10);

            // 加载检测器配置
            if (auto detectors = processor->operator[]("detectors")) {
                for (const auto& key : detectors->getKeys()) {
                    auto detector_config = detectors->operator[](key);

                    DetectorConfig detector;
                    detector.type = detector_config->operator[]("type")->asString();
                    detector.model_path = detector_config->operator[]("model_path")->asString();
                    detector.config_path = detector_config->operator[]("config_path")->asString();
                    detector.names_path = detector_config->operator[]("names_path")->asString();
                    detector.confidence_threshold = detector_config->operator[]("confidence_threshold")->asDouble(0.5);
                    detector.nms_threshold = detector_config->operator[]("nms_threshold")->asDouble(0.4);

                    config.processor.detectors[key] = detector;
                }
            }
        }

        // 加载应用配置
        if (auto app = yaml->operator[]("app")) {
            config.app.enable_detection = app->operator[]("enable_detection")->asBool(true);
            config.app.show_detection_info = app->operator[]("show_detection_info")->asBool(true);
        }

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Error parsing config file: " << e.what() << std::endl;
        return false;
    }
}

Config ConfigLoader::getDefaultConfig() {
    Config config;

    // 默认流配置
    config.stream.input_stream = "test.mp4";
    config.stream.output_stream = "rtmp://localhost/live/test";
    config.stream.output_width = 1920;
    config.stream.output_height = 1080;
    config.stream.fps = 30.0;
    config.stream.maintain_aspect_ratio = true;

    // 默认编码配置
    config.stream.encoding.keyframe_interval = 2.0;
    config.stream.encoding.force_keyframes = false;
    config.stream.encoding.preset = "medium";
    config.stream.encoding.profile = "main";
    config.stream.encoding.level = "4.0";
    config.stream.encoding.zero_latency = false;
    config.stream.encoding.refs = 3;
    config.stream.encoding.bframes = 3;

    // 默认处理器配置
    config.processor.num_threads = 4;
    config.processor.input_queue_size = 10;
    config.processor.output_queue_size = 10;

    // 默认检测器配置
    DetectorConfig detector;
    detector.type = "test";
    detector.model_path = "";
    detector.config_path = "";
    detector.names_path = "";
    detector.confidence_threshold = 0.5f;
    detector.nms_threshold = 0.4f;
    config.processor.detectors["detector_0"] = detector;

    // 默认应用配置
    config.app.enable_detection = true;
    config.app.show_detection_info = true;

    return config;
}