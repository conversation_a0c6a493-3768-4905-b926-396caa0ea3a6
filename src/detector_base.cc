#include "detector_base.h"
#include "yolo_detector.h"
#include "tensorrt_yolo_detector.h"
#include <iostream>

std::unique_ptr<DetectorBase> DetectorFactory::createDetector(const std::string& type,
                                                             const std::string& model_path,
                                                             const std::string& config_path,
                                                             const std::string& names_path) {
    if (type == "yolo" || type == "YOLO") {
        auto detector = std::make_unique<YOLODetector>(model_path, config_path);
        if (detector->initialize()) {
            return std::move(detector);
        } else {
            std::cerr << "Failed to initialize YOLO detector" << std::endl;
            return nullptr;
        }
    }
    else if (type == "tensorrt" || type == "trt" || type == "tensorrt_yolo") {
        auto detector = std::make_unique<TensorRTYOLODetector>(model_path, config_path);
        if (detector->initialize()) {
            return std::move(detector);
        } else {
            std::cerr << "Failed to initialize TensorRT YOLO detector" << std::endl;
            return nullptr;
        }
    }
    // 可以在这里添加其他类型的检测器
    else if (type == "test") {
        // 创建一个测试用的虚拟检测器
        class DummyDetector : public DetectorBase {
        public:
            DummyDetector() : DetectorBase("DummyDetector") {}
            
            bool initialize() override {
                std::cout << "DummyDetector initialized" << std::endl;
                return true;
            }
            
            std::vector<DetectionResult> detect(const cv::Mat& frame) override {
                std::vector<DetectionResult> results;
                // 创建一个虚拟的检测结果
                if (!frame.empty()) {
                    int w = frame.cols;
                    int h = frame.rows;
                    cv::Rect bbox(w/4, h/4, w/2, h/2);
                    results.emplace_back(bbox, 0.8f, 0, "dummy_object_测试");
                }
                return results;
            }
            
            void cleanup() override {
                std::cout << "DummyDetector cleaned up" << std::endl;
            }
        };
        
        auto detector = std::make_unique<DummyDetector>();
        if (detector->initialize()) {
            return std::move(detector);
        }
    }
    printf("Detector type: %d\n",type == "test");
    
    std::cerr << "Unknown detector type: " << type << std::endl;
    return nullptr;
}
