#include "ffmpeg_reader.h"
#include <iostream>

FFmpegReader::FFmpegReader()
    : format_context_(nullptr), codec_context_(nullptr), codec_(nullptr),
      frame_(nullptr), frame_bgr_(nullptr), packet_(nullptr), sws_context_(nullptr),
      video_stream_index_(-1), width_(0), height_(0), fps_(0.0),
      resolution_changed_(false), last_width_(0), last_height_(0), should_interrupt_(false) {

    // 新版本FFmpeg不需要av_register_all()
    // av_register_all(); // 在FFmpeg 4.0+中已废弃
    avformat_network_init();
}

FFmpegReader::~FFmpegReader() {
    close();
}

bool FFmpegReader::open(const std::string& url) {
    close(); // 确保之前的资源已释放
    should_interrupt_ = false; // 重置中断标志

    // 分配格式上下文
    format_context_ = avformat_alloc_context();
    if (!format_context_) {
        std::cerr << "Failed to allocate format context" << std::endl;
        return false;
    }

    // 设置中断回调
    format_context_->interrupt_callback.callback = interruptCallback;
    format_context_->interrupt_callback.opaque = this;

    // 设置连接选项
    AVDictionary* options = nullptr;
    // av_dict_set(&options, "timeout", "15000000", 0);  // 15秒超时
    av_dict_set(&options, "rtmp_live", "1", 0);  // RTMP直播模式
    // av_dict_set(&options, "rtmp_playpath", "", 0);  // 清空playpath
    // av_dict_set(&options, "rtmp_buffer", "1000", 0);  // 1秒缓冲

    std::cout << "Attempting to open: " << url << std::endl;

    // 检查是否是测试源
    const AVInputFormat* input_format = nullptr;
    if (url.find("testsrc") != std::string::npos) {
        input_format = av_find_input_format("lavfi");
        std::cout << "Using lavfi input format for test source" << std::endl;
    }

    // 打开输入流
    int ret = avformat_open_input(&format_context_, url.c_str(), input_format, &options);
    av_dict_free(&options);

    if (ret < 0) {
        char error_buf[256];
        av_strerror(ret, error_buf, sizeof(error_buf));
        std::cerr << "Failed to open input: " << url << std::endl;
        std::cerr << "Error details: " << error_buf << std::endl;
        return false;
    }

    std::cout << "Successfully connected to stream" << std::endl;
    
    // 获取流信息
    if (avformat_find_stream_info(format_context_, nullptr) < 0) {
        std::cerr << "Failed to find stream info" << std::endl;
        cleanup();
        return false;
    }
    
    // 查找视频流
    video_stream_index_ = -1;
    std::cout << "Found " << format_context_->nb_streams << " streams:" << std::endl;

    for (unsigned int i = 0; i < format_context_->nb_streams; i++) {
        AVCodecParameters* codecpar = format_context_->streams[i]->codecpar;
        const char* media_type = av_get_media_type_string(codecpar->codec_type);
        const AVCodec* codec = avcodec_find_decoder(codecpar->codec_id);
        const char* codec_name = codec ? codec->name : "unknown";

        std::cout << "  Stream " << i << ": " << (media_type ? media_type : "unknown")
                 << " (" << codec_name << ")" << std::endl;

        if (codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index_ = i;
            std::cout << "    -> Selected as video stream" << std::endl;
            break;
        }
    }

    if (video_stream_index_ == -1) {
        std::cerr << "No video stream found in " << format_context_->nb_streams << " streams" << std::endl;
        cleanup();
        return false;
    }
    
    // 初始化解码器
    if (!initializeDecoder()) {
        cleanup();
        return false;
    }
    
    // 获取视频信息
    AVStream* video_stream = format_context_->streams[video_stream_index_];
    width_ = codec_context_->width;
    height_ = codec_context_->height;
    
    // 计算帧率
    if (video_stream->r_frame_rate.den > 0) {
        fps_ = static_cast<double>(video_stream->r_frame_rate.num) / video_stream->r_frame_rate.den;
    } else {
        fps_ = 30.0; // 默认帧率
    }
    
    std::cout << "Successfully opened: " << url << std::endl;
    std::cout << "Video info: " << width_ << "x" << height_ << ", fps: " << fps_ << std::endl;
    
    return true;
}

bool FFmpegReader::initializeDecoder() {
    AVCodecParameters* codec_params = format_context_->streams[video_stream_index_]->codecpar;

    // 查找解码器 (新版本返回const AVCodec*)
    const AVCodec* codec = avcodec_find_decoder(codec_params->codec_id);
    if (!codec) {
        std::cerr << "Codec not found" << std::endl;
        return false;
    }
    codec_ = const_cast<AVCodec*>(codec);
    
    // 分配解码器上下文
    codec_context_ = avcodec_alloc_context3(codec_);
    if (!codec_context_) {
        std::cerr << "Failed to allocate codec context" << std::endl;
        return false;
    }
    
    // 复制参数到解码器上下文
    if (avcodec_parameters_to_context(codec_context_, codec_params) < 0) {
        std::cerr << "Failed to copy codec parameters" << std::endl;
        return false;
    }
    
    // 打开解码器
    if (avcodec_open2(codec_context_, codec_, nullptr) < 0) {
        std::cerr << "Failed to open codec" << std::endl;
        return false;
    }
    
    // 分配帧
    frame_ = av_frame_alloc();
    frame_bgr_ = av_frame_alloc();
    packet_ = av_packet_alloc();
    
    if (!frame_ || !frame_bgr_ || !packet_) {
        std::cerr << "Failed to allocate frames or packet" << std::endl;
        return false;
    }
    
    // 为BGR帧分配缓冲区
    int num_bytes = av_image_get_buffer_size(AV_PIX_FMT_BGR24, codec_context_->width, codec_context_->height, 1);
    uint8_t* buffer = (uint8_t*)av_malloc(num_bytes * sizeof(uint8_t));
    av_image_fill_arrays(frame_bgr_->data, frame_bgr_->linesize, buffer, AV_PIX_FMT_BGR24, 
                        codec_context_->width, codec_context_->height, 1);
    
    // 初始化转换上下文
    sws_context_ = sws_getContext(codec_context_->width, codec_context_->height, codec_context_->pix_fmt,
                                 codec_context_->width, codec_context_->height, AV_PIX_FMT_BGR24,
                                 SWS_BILINEAR, nullptr, nullptr, nullptr);
    
    if (!sws_context_) {
        std::cerr << "Failed to initialize sws context" << std::endl;
        return false;
    }
    
    return true;
}

bool FFmpegReader::reinitializeScaler(int new_width, int new_height) {
    // 释放旧的scaler
    if (sws_context_) {
        sws_freeContext(sws_context_);
        sws_context_ = nullptr;
    }

    // 释放旧的BGR帧缓冲区
    if (frame_bgr_ && frame_bgr_->data[0]) {
        av_free(frame_bgr_->data[0]);
        frame_bgr_->data[0] = nullptr;
    }

    // 为新分辨率分配BGR帧缓冲区
    int num_bytes = av_image_get_buffer_size(AV_PIX_FMT_BGR24, new_width, new_height, 1);
    uint8_t* buffer = (uint8_t*)av_malloc(num_bytes * sizeof(uint8_t));
    if (!buffer) {
        std::cerr << "Failed to allocate buffer for new resolution" << std::endl;
        return false;
    }

    av_image_fill_arrays(frame_bgr_->data, frame_bgr_->linesize, buffer, AV_PIX_FMT_BGR24,
                        new_width, new_height, 1);

    // 创建新的scaler上下文
    sws_context_ = sws_getContext(new_width, new_height, codec_context_->pix_fmt,
                                 new_width, new_height, AV_PIX_FMT_BGR24,
                                 SWS_BILINEAR, nullptr, nullptr, nullptr);

    if (!sws_context_) {
        std::cerr << "Failed to create new sws context for resolution "
                 << new_width << "x" << new_height << std::endl;
        return false;
    }

    std::cout << "Successfully reinitialized scaler for " << new_width << "x" << new_height << std::endl;
    return true;
}

bool FFmpegReader::readFrame(cv::Mat& frame) {
    if (!format_context_ || !codec_context_) {
        return false;
    }

    if (should_interrupt_) {
        return false;
    }

    while (av_read_frame(format_context_, packet_) >= 0) {
        if (should_interrupt_) {
            av_packet_unref(packet_);
            return false;
        }
        if (packet_->stream_index == video_stream_index_) {
            // 发送包到解码器
            int ret = avcodec_send_packet(codec_context_, packet_);
            if (ret < 0) {
                av_packet_unref(packet_);
                continue;
            }

            // 接收解码后的帧
            ret = avcodec_receive_frame(codec_context_, frame_);
            if (ret == 0) {
                // 检查分辨率是否发生变化
                int current_width = frame_->width;
                int current_height = frame_->height;

                if (current_width != last_width_ || current_height != last_height_) {
                    std::cout << "Resolution changed from " << last_width_ << "x" << last_height_
                             << " to " << current_width << "x" << current_height << std::endl;

                    // 重新初始化scaler
                    if (!reinitializeScaler(current_width, current_height)) {
                        std::cerr << "Failed to reinitialize scaler for new resolution" << std::endl;
                        av_packet_unref(packet_);
                        return false;
                    }

                    // 更新分辨率信息
                    width_ = current_width;
                    height_ = current_height;
                    last_width_ = current_width;
                    last_height_ = current_height;
                    resolution_changed_ = true;
                }

                // 转换像素格式为BGR
                sws_scale(sws_context_, frame_->data, frame_->linesize, 0, current_height,
                         frame_bgr_->data, frame_bgr_->linesize);

                // 创建OpenCV Mat
                frame = cv::Mat(current_height, current_width, CV_8UC3, frame_bgr_->data[0], frame_bgr_->linesize[0]).clone();

                av_packet_unref(packet_);
                return true;
            }
        }
        av_packet_unref(packet_);
    }

    return false; // 到达流末尾或出错
}

void FFmpegReader::close() {
    cleanup();
}

void FFmpegReader::cleanup() {
    should_interrupt_ = true; // 设置中断标志

    if (sws_context_) {
        sws_freeContext(sws_context_);
        sws_context_ = nullptr;
    }

    if (frame_bgr_) {
        if (frame_bgr_->data[0]) {
            av_free(frame_bgr_->data[0]);
        }
        av_frame_free(&frame_bgr_);
    }

    if (frame_) {
        av_frame_free(&frame_);
    }

    if (packet_) {
        av_packet_free(&packet_);
    }

    if (codec_context_) {
        avcodec_free_context(&codec_context_);
    }

    if (format_context_) {
        avformat_close_input(&format_context_);
    }

    video_stream_index_ = -1;
    width_ = height_ = 0;
    fps_ = 0.0;
}

// FFmpeg中断回调函数
int FFmpegReader::interruptCallback(void* ctx) {
    FFmpegReader* reader = static_cast<FFmpegReader*>(ctx);
    return reader->should_interrupt_ ? 1 : 0;
}
