#include <iostream>
#include <csignal>
#include <opencv2/opencv.hpp>
#include <sstream>
#include <string>
#include <memory>
#include <chrono>
#include <cstdlib>
#include <iomanip>
#include <thread>
#include "frame_processor.h"
#include "detector_base.h"
#include "detection_config.h"
#include "ffmpeg_reader.h"
#include "tensorrt_yolo_detector.h"

bool is_running = true;
std::unique_ptr<FrameProcessor> frame_processor;
std::unique_ptr<FFmpegReader> reader_ptr;
FILE* ffmpeg_process = nullptr;

void OnSignal(int signal_num) {
    std::cout << "\nReceived signal " << signal_num << ", shutting down..." << std::endl;
    is_running = false;

    if (frame_processor) {
        frame_processor->stop();
    }

    // 中断FFmpeg读取器
    if (reader_ptr) {
        reader_ptr->interrupt();
    }

    // 强制关闭FFmpeg进程
    if (ffmpeg_process) {
        pclose(ffmpeg_process);
        ffmpeg_process = nullptr;
    }

    // 如果多次按Ctrl+C，强制退出
    static int signal_count = 0;
    signal_count++;
    if (signal_count >= 3) {
        std::cout << "Force exit after multiple signals" << std::endl;
        exit(EXIT_FAILURE);
    }
}

int main() {
    // 触发下面的信号就退出
    signal(SIGINT, OnSignal);
    signal(SIGQUIT, OnSignal);
    signal(SIGTERM, OnSignal);

    // 加载配置文件
    std::string config_file = "config.yaml";
    Config config;
    if (!ConfigLoader::loadFromFile(config_file, config)) {
        std::cerr << "Failed to load config file, using default config" << std::endl;
        config = ConfigLoader::getDefaultConfig();
    }

    // 从配置获取流地址和输出分辨率
    std::string input_stream = config.stream.input_stream;
    std::string output_stream = config.stream.output_stream;
    const int output_width = config.stream.output_width;
    const int output_height = config.stream.output_height;
    const bool maintain_aspect_ratio = config.stream.maintain_aspect_ratio;

    std::cout << "Configuration loaded:" << std::endl;
    std::cout << "  Input stream: " << input_stream << std::endl;
    std::cout << "  Output stream: " << output_stream << std::endl;
    std::cout << "  Output resolution: " << output_width << "x" << output_height << std::endl;
    std::cout << "  Maintain aspect ratio: " << (maintain_aspect_ratio ? "Yes" : "No") << std::endl;

    // 初始化帧处理器
    frame_processor = std::make_unique<FrameProcessor>(config.processor.num_threads);

    // 保存TensorRT检测器的引用用于性能统计
    std::vector<TensorRTYOLODetector*> tensorrt_detectors;

    // 添加配置中的检测器
    for (const auto& [name, detector_config] : config.processor.detectors) {
        auto detector = DetectorFactory::createDetector(detector_config.type,
                                                       detector_config.model_path,
                                                       detector_config.config_path,
                                                       detector_config.names_path);
        if (detector) {
            detector->setConfidenceThreshold(detector_config.confidence_threshold);
            detector->setNMSThreshold(detector_config.nms_threshold);

            // 如果是TensorRT检测器，保存引用
            if (detector_config.type == "tensorrt" || detector_config.type == "trt" || detector_config.type == "tensorrt_yolo") {
                auto* trt_detector = dynamic_cast<TensorRTYOLODetector*>(detector.get());
                if (trt_detector) {
                    tensorrt_detectors.push_back(trt_detector);
                }
            }

            frame_processor->addDetector(std::move(detector));
        } else {
            std::cerr << "Failed to create detector: " << detector_config.type << std::endl;
        }
    }

    // 启动帧处理器
    if (!frame_processor->start()) {
        std::cerr << "Failed to start frame processor" << std::endl;
        return EXIT_FAILURE;
    }

    // 使用FFmpeg开发包读取输入流
    reader_ptr = std::make_unique<FFmpegReader>();
    if (!reader_ptr->open(input_stream)) {
        std::cerr << "Failed to open input stream: " << input_stream << std::endl;
        return EXIT_FAILURE;
    }

    std::cout << "成功打开输入流: " << input_stream << std::endl;

    // 从FFmpeg读取器获取视频信息
    int original_width = reader_ptr->getWidth();
    int original_height = reader_ptr->getHeight();
    double fps = reader_ptr->getFPS();

    if (fps <= 0) fps = 30;  // 确保帧率有效
    std::cout << "FPS: " << fps << std::endl;
    std::cout << "Input stream info: " << original_width << "x" << original_height
              << ", fps: " << fps << std::endl;
    std::cout << "Output stream will be resized to: " << output_width << "x" << output_height << std::endl;

    // 构建FFmpeg命令
    std::stringstream command;
    command << "ffmpeg ";
    // 输入选项
    command << "-y "                  // 覆盖输出文件
    << "-re "        
            << "-f rawvideo "         // 强制格式为原始视频
            << "-vcodec rawvideo "    // 视频编解码器为原始视频
            << "-pix_fmt bgr24 "      // 像素格式为bgr24
            << "-s " << output_width << "x" << output_height << " "  // 帧尺寸
            << "-r " << fps << " "    // 帧率
            << "-fflags +nobuffer "   // 禁用缓冲以减少延时
            << "-flags +low_delay "   // 低延时标志
            << "-i - ";               // 从标准输入读取

    // 输出选项 - 使用配置中的编码参数防止黑屏
    const auto& encoding = config.stream.encoding;
    int keyframe_interval = static_cast<int>(fps * encoding.keyframe_interval);

    command << "-c:v libx264 "        // 使用x264编码
            << "-pix_fmt yuv420p "   // 输出像素格式为yuv420p
            << "-preset " << encoding.preset << " "  // 编码预设
            << "-profile:v " << encoding.profile << " "  // H.264配置文件
            << "-level " << encoding.level << " ";      // H.264级别

    // 零延迟调优
    if (encoding.zero_latency) {
        command << "-tune zerolatency ";
    }

    // 关键帧设置
    command << "-g " << keyframe_interval << " "  // 关键帧间隔
            << "-keyint_min " << (keyframe_interval / 2) << " "  // 最小关键帧间隔
            << "-sc_threshold 0 ";     // 禁用场景切换检测，强制固定关键帧间隔

    // 强制关键帧
    if (encoding.force_keyframes) {
        command << "-force_key_frames \"expr:gte(t,n_forced*" << encoding.keyframe_interval << ")\" ";
    }

    // 参考帧和B帧设置 - 实时流优化配置
    command << "-refs " << encoding.refs << " "    // 参考帧数量
            << "-bf " << encoding.bframes << " "   // B帧数量
            << "-flush_packets 1 "   // 立即刷新包
            << "-fflags +flush_packets " // 强制刷新包
            << "-max_delay 0 "       // 最大延时为0
            << "-bufsize 32k "       // 适中缓冲区
            << "-maxrate 300k "      // 适中最大码率
            << "-b:v 200k "          // 适中目标码率
            << "-minrate 150k "      // 适中最小码率
            << "-muxdelay 0 "        // 减少复用延时
            << "-muxpreload 0 "      // 减少预加载
            << "-avoid_negative_ts make_zero " // 避免负时间戳
            << "-fflags +genpts "    // 生成PTS
            << "-threads 2 "         // 减少编码线程
            << "-thread_type slice " // 使用slice线程
            << "-slices 2 "          // 减少slice数量
            << "-crf 26 "            // 适中压缩率（平衡质量和大小）
            << "-tune zerolatency "  // 零延迟调优
            << "-x264opts keyint=30:min-keyint=15:no-scenecut " // 稳定关键帧
            << "-rc_lookahead 10 "   // 少量前瞻以稳定质量
            << "-sc_threshold 0 "    // 禁用场景切换检测
            << "-qmin 18 "           // 设置最小量化参数
            << "-qmax 32 "           // 设置最大量化参数
            << "-rtmp_live 1 "       // RTMP直播模式
            << "-rtmp_buffer 100 "   // RTMP缓冲区大小(ms)
            << "-f flv "             // 强制格式为flv
            << output_stream;

    // 打开FFmpeg进程
    ffmpeg_process = popen(command.str().c_str(), "w");
    if (!ffmpeg_process) {
        std::cerr << "Failed to open FFmpeg process" << std::endl;
        return EXIT_FAILURE;
    }

    cv::Mat frame;
    int frame_id = 0;
    int dropped_frames = 0;
    auto start_time = std::chrono::high_resolution_clock::now();
    auto last_stats_time = start_time;
    auto last_frame_time = start_time;

    // 计算目标帧间隔（如果fps > 0）
    double target_frame_interval_ms = 0.0;
    if (config.stream.fps > 0) {
        target_frame_interval_ms = 1000.0 / config.stream.fps;
    }

    while (is_running) {
        // 检查是否需要退出
        if (!is_running) {
            break;
        }

        // 使用FFmpeg读取器读取帧
        if (!reader_ptr->readFrame(frame)) {
            std::cout << "End of input stream reached or read error" << std::endl;
            break;
        }

        // 再次检查是否需要退出
        if (!is_running) {
            break;
        }

        if (frame.empty()) {
            std::cerr << "Received empty frame" << std::endl;
            continue;
        }

        // 检查分辨率是否发生变化
        if (reader_ptr->hasResolutionChanged()) {
            int new_width = reader_ptr->getWidth();
            int new_height = reader_ptr->getHeight();

            std::cout << "Detected resolution change to " << new_width << "x" << new_height << std::endl;
            std::cout << "Original resolution was " << original_width << "x" << original_height << std::endl;

            // 更新原始分辨率信息
            original_width = new_width;
            original_height = new_height;

            // 重置分辨率变化标志
            reader_ptr->resetResolutionFlag();

            std::cout << "Continuing with new resolution..." << std::endl;
        }

        // 计算时间戳
        auto current_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - start_time);
        double timestamp = duration.count() / 1000.0;

        // 更激进的跳帧策略 - 如果队列有任何积压就跳帧
        if (frame_processor->getInputQueueSize() >= 1) {
            dropped_frames++;

            // 直接处理原始帧，跳过检测以提高速度
            cv::Mat resized_frame = FrameProcessor::resizeFrame(frame, output_width, output_height, maintain_aspect_ratio);

            // 确保帧数据连续性
            if (!resized_frame.isContinuous()) {
                resized_frame = resized_frame.clone();
            }

            // 写入FFmpeg进程
            if (ffmpeg_process && is_running) {
                size_t frame_size = resized_frame.total() * resized_frame.elemSize();
                fwrite(resized_frame.data, 1, frame_size, ffmpeg_process);

                // 减少刷新频率
                if (frame_id % 10 == 0) {
                    fflush(ffmpeg_process);
                }
            }

            frame_id++;
            continue;
        }

        // 提交帧进行处理
        if (!frame_processor->submitFrame(frame, frame_id++, timestamp)) {
            std::cerr << "Failed to submit frame " << frame_id << std::endl;
            continue;
        }

        // 检查是否需要退出
        if (!is_running) {
            break;
        }

        // 获取处理结果
        FrameDetectionResult result;
        cv::Mat output_frame;

        if (frame_processor->getResult(result, 5)) { // 减少超时到5ms
            // 使用处理后的帧（带检测框）
            output_frame = result.processed_frame;

            // 打印检测信息
            // if (!result.detections.empty()) {
            //     std::cout << "Frame " << result.frame_id << ": detected "
            //              << result.detections.size() << " objects" << std::endl;
            // }
        } else {
            // 如果没有及时获得处理结果，使用原始帧
            output_frame = frame;
        }

        // 再次检查是否需要退出
        if (!is_running) {
            break;
        }

        // 缩放到固定分辨率
        cv::Mat resized_frame = FrameProcessor::resizeFrame(output_frame, output_width, output_height, maintain_aspect_ratio);

        // 验证帧数据有效性
        if (resized_frame.empty() || resized_frame.cols != output_width || resized_frame.rows != output_height) {
            std::cerr << "Invalid frame data, skipping frame " << frame_id << std::endl;
            continue;
        }

        // 确保帧数据连续性
        if (!resized_frame.isContinuous()) {
            resized_frame = resized_frame.clone();
        }

        // 将缩放后的帧写入FFmpeg进程
        if (ffmpeg_process && is_running) {
            auto write_start = std::chrono::high_resolution_clock::now();

            size_t frame_size = resized_frame.total() * resized_frame.elemSize();
            size_t written = fwrite(resized_frame.data, 1, frame_size, ffmpeg_process);

            if (written != frame_size) {
                std::cerr << "Failed to write complete frame " << frame_id
                         << " (wrote " << written << "/" << frame_size << " bytes)" << std::endl;
                // 如果写入失败，可能是管道已关闭，退出循环
                if (!is_running) break;
            }

            // 进一步减少刷新频率，每20帧刷新一次以提高性能
            if (frame_id % 20 == 0) {
                if (fflush(ffmpeg_process) != 0) {
                    std::cerr << "Failed to flush frame " << frame_id << std::endl;
                    // 如果刷新失败，可能是管道已关闭，退出循环
                    if (!is_running) break;
                }
            }

            auto write_end = std::chrono::high_resolution_clock::now();
            auto write_duration = std::chrono::duration_cast<std::chrono::microseconds>(write_end - write_start);

            // 每100帧输出一次写入时间统计
            static double total_write_time = 0;
            static int write_count = 0;
            total_write_time += write_duration.count() / 1000.0;
            write_count++;

            if (frame_id % 100 == 0) {
                double avg_write_time = total_write_time / write_count;
                std::cout << "Average FFmpeg write time: " << std::fixed << std::setprecision(2)
                         << avg_write_time << " ms (target: <5ms for real-time)" << std::endl;

                // 如果写入时间过长，给出警告
                if (avg_write_time > 10.0) {
                    std::cout << "⚠️  WARNING: FFmpeg write time is high, network may be slow" << std::endl;
                }

                total_write_time = 0;
                write_count = 0;
            }
        }

        // 帧率控制 - 如果处理太快，适当延时
        if (target_frame_interval_ms > 0) {
            auto current_time = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_frame_time);
            double elapsed_ms = elapsed.count();

            if (elapsed_ms < target_frame_interval_ms) {
                double sleep_ms = target_frame_interval_ms - elapsed_ms;
                if (sleep_ms > 1.0) { // 只有当需要睡眠超过1ms时才睡眠
                    std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(sleep_ms)));
                }
            }
            last_frame_time = std::chrono::high_resolution_clock::now();
        }

        // 每30帧输出一次状态信息，包括延时统计
        if (frame_id % 30 == 0) {
            auto stats_time = std::chrono::high_resolution_clock::now();
            auto stats_duration = std::chrono::duration_cast<std::chrono::milliseconds>(stats_time - last_stats_time);
            double actual_fps = 30.0 / (stats_duration.count() / 1000.0);

            // 计算丢帧率和处理效率
            double drop_rate = (double)dropped_frames / frame_id * 100.0;
            double processing_efficiency = actual_fps / config.stream.fps * 100.0;

            std::cout << "Frame " << frame_id << ", timestamp: " << timestamp << "s, "
                     << "actual FPS: " << std::fixed << std::setprecision(1) << actual_fps
                     << " (efficiency: " << std::setprecision(1) << processing_efficiency << "%), "
                     << "dropped: " << dropped_frames << " (" << std::setprecision(1) << drop_rate << "%), "
                     << "input queue: " << frame_processor->getInputQueueSize() << ", "
                     << "output queue: " << frame_processor->getOutputQueueSize();

            // 显示TensorRT性能统计
            for (size_t i = 0; i < tensorrt_detectors.size(); ++i) {
                auto stats = tensorrt_detectors[i]->getPerformanceStats();
                if (stats.total_inferences > 0) {
                    std::cout << ", TRT" << i << " avg: " << std::fixed << std::setprecision(1)
                             << stats.avg_inference_time_ms << "ms"
                             << " (min: " << std::setprecision(1) << stats.min_inference_time_ms << "ms"
                             << ", max: " << std::setprecision(1) << stats.max_inference_time_ms << "ms"
                             << ", count: " << stats.total_inferences << ")";
                }
            }
            std::cout << std::endl;

            // 推流速度预警和建议 - 减少频率
            static int slow_stream_count = 0;
            if (actual_fps > 20 && frame_id > 600) { // 处理正常但推流可能慢
                slow_stream_count++;
                if (slow_stream_count >= 10) { // 增加触发阈值
                    std::cout << "\n🔍 NETWORK DIAGNOSIS:" << std::endl;
                    std::cout << "   ✅ Local processing: FAST (" << std::setprecision(1) << actual_fps << " FPS)" << std::endl;
                    std::cout << "   ✅ FFmpeg encoding: FAST (1-2ms)" << std::endl;
                    std::cout << "   ❌ Network upload: SLOW (0.5x speed)" << std::endl;
                    std::cout << "\n💡 SOLUTIONS:" << std::endl;
                    std::cout << "   1. Check network bandwidth: ping/speedtest" << std::endl;
                    std::cout << "   2. Try different RTMP server" << std::endl;
                    std::cout << "   3. Reduce bitrate further (current: ~200kbps)" << std::endl;
                    std::cout << "   4. Use UDP instead of RTMP if possible" << std::endl;
                    std::cout << "   5. Current optimization: 87% bitrate reduction achieved!" << std::endl;
                    slow_stream_count = 0; // 重置计数器
                }
            }

            // 推流速度趋势监控
            static double last_speed_estimate = 0.0;
            double current_speed_estimate = actual_fps / 30.0; // 粗略估算
            if (last_speed_estimate > 0 && current_speed_estimate > last_speed_estimate * 1.1) {
                std::cout << "📈 Streaming speed improving!" << std::endl;
            }
            last_speed_estimate = current_speed_estimate;

            last_stats_time = stats_time;
        }
    }

    // 显示最终性能统计
    std::cout << "\n=== Final Performance Statistics ===" << std::endl;
    for (size_t i = 0; i < tensorrt_detectors.size(); ++i) {
        auto stats = tensorrt_detectors[i]->getPerformanceStats();
        if (stats.total_inferences > 0) {
            std::cout << "TensorRT Detector " << i << ":" << std::endl;
            std::cout << "  Total inferences: " << stats.total_inferences << std::endl;
            std::cout << "  Average time: " << std::fixed << std::setprecision(2)
                     << stats.avg_inference_time_ms << " ms" << std::endl;
            std::cout << "  Min time: " << std::setprecision(2)
                     << stats.min_inference_time_ms << " ms" << std::endl;
            std::cout << "  Max time: " << std::setprecision(2)
                     << stats.max_inference_time_ms << " ms" << std::endl;
            std::cout << "  Total time: " << std::setprecision(2)
                     << stats.total_time_ms << " ms" << std::endl;
            std::cout << "  Average FPS: " << std::setprecision(1)
                     << (1000.0 / stats.avg_inference_time_ms) << std::endl;
        }
    }

    // 清理资源
    std::cout << "\nCleaning up resources..." << std::endl;

    if (frame_processor) {
        frame_processor->stop();
        frame_processor.reset();
    }

    if (ffmpeg_process) {
        pclose(ffmpeg_process);
        ffmpeg_process = nullptr;
    }

    if (reader_ptr) {
        reader_ptr->close();
        reader_ptr.reset();
    }

    std::cout << "Streaming finished" << std::endl;
    return EXIT_SUCCESS;
}