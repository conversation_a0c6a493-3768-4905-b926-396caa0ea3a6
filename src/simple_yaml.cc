#include "simple_yaml.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iostream>

std::shared_ptr<SimpleYAML> SimpleYAML::loadFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return nullptr;
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    return parse(content);
}

std::shared_ptr<SimpleYAML> SimpleYAML::parse(const std::string& content) {
    // 简化的YAML解析器实现
    auto result = std::make_shared<SimpleYAML>();
    result->value_ = std::map<std::string, std::shared_ptr<SimpleYAML>>();

    std::vector<std::string> lines;
    std::stringstream ss(content);
    std::string line;

    while (std::getline(ss, line)) {
        lines.push_back(line);
    }

    size_t lineIndex = 0;
    result = parseMap(lines, lineIndex, 0);

    return result;
}

std::shared_ptr<SimpleYAML> SimpleYAML::parseMap(const std::vector<std::string>& lines, size_t& lineIndex, int baseIndent) {
    auto result = std::make_shared<SimpleYAML>();
    result->value_ = std::map<std::string, std::shared_ptr<SimpleYAML>>();

    while (lineIndex < lines.size()) {
        const std::string& line = lines[lineIndex];

        if (trim(line).empty() || trim(line)[0] == '#') {
            lineIndex++;
            continue;
        }

        int indent = getIndentLevel(line);
        if (indent < baseIndent) {
            break;
        }

        if (indent > baseIndent) {
            lineIndex++;
            continue;
        }

        size_t colonPos = line.find(':');
        if (colonPos != std::string::npos) {
            std::string key = trim(line.substr(indent, colonPos - indent));
            std::string valueStr = trim(line.substr(colonPos + 1));

            if (valueStr.empty()) {
                // 嵌套对象
                lineIndex++;
                auto nestedMap = parseMap(lines, lineIndex, indent + 2);
                std::get<std::map<std::string, std::shared_ptr<SimpleYAML>>>(result->value_)[key] = nestedMap;
            } else {
                // 简单值
                auto value = std::make_shared<SimpleYAML>();
                value->value_ = valueStr;
                std::get<std::map<std::string, std::shared_ptr<SimpleYAML>>>(result->value_)[key] = value;
                lineIndex++;
            }
        } else {
            lineIndex++;
        }
    }

    return result;
}

std::shared_ptr<SimpleYAML> SimpleYAML::operator[](const std::string& key) const {
    if (isMap()) {
        const auto& map = std::get<std::map<std::string, std::shared_ptr<SimpleYAML>>>(value_);
        auto it = map.find(key);
        if (it != map.end()) {
            return it->second;
        }
    }
    return std::make_shared<SimpleYAML>(); // 返回空对象
}

bool SimpleYAML::isString() const {
    return std::holds_alternative<std::string>(value_);
}

bool SimpleYAML::isMap() const {
    return std::holds_alternative<std::map<std::string, std::shared_ptr<SimpleYAML>>>(value_);
}

std::string SimpleYAML::asString(const std::string& defaultValue) const {
    if (isString()) {
        return std::get<std::string>(value_);
    }
    return defaultValue;
}

int SimpleYAML::asInt(int defaultValue) const {
    if (isString()) {
        try {
            return std::stoi(std::get<std::string>(value_));
        } catch (...) {
            return defaultValue;
        }
    }
    return defaultValue;
}

double SimpleYAML::asDouble(double defaultValue) const {
    if (isString()) {
        try {
            return std::stod(std::get<std::string>(value_));
        } catch (...) {
            return defaultValue;
        }
    }
    return defaultValue;
}

bool SimpleYAML::asBool(bool defaultValue) const {
    if (isString()) {
        std::string str = std::get<std::string>(value_);
        std::transform(str.begin(), str.end(), str.begin(), ::tolower);
        if (str == "true" || str == "yes" || str == "1") {
            return true;
        } else if (str == "false" || str == "no" || str == "0") {
            return false;
        }
    }
    return defaultValue;
}

std::vector<std::string> SimpleYAML::getKeys() const {
    std::vector<std::string> keys;
    if (isMap()) {
        const auto& map = std::get<std::map<std::string, std::shared_ptr<SimpleYAML>>>(value_);
        for (const auto& pair : map) {
            keys.push_back(pair.first);
        }
    }
    return keys;
}

int SimpleYAML::getIndentLevel(const std::string& line) {
    int indent = 0;
    for (char c : line) {
        if (c == ' ') {
            indent++;
        } else {
            break;
        }
    }
    return indent;
}

std::string SimpleYAML::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}