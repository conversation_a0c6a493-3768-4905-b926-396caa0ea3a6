#include "yolo_detector.h"
#include <iostream>
#include <algorithm>

YOLODetector::YOLODetector(const std::string& model_path, const std::string& config_path)
    : DetectorBase("YOLODetector"), model_path_(model_path), config_path_(config_path) {
}

YOLODetector::~YOLODetector() {
    cleanup();
}

bool YOLODetector::initialize() {
    try {
        if (model_path_.empty()) {
            std::cerr << "Model path is empty" << std::endl;
            return false;
        }
        
        // 根据文件扩展名选择加载方式
        if (model_path_.find(".onnx") != std::string::npos) {
            net_ = cv::dnn::readNetFromONNX(model_path_);
        } else if (model_path_.find(".weights") != std::string::npos) {
            if (config_path_.empty()) {
                std::cerr << "Config path required for .weights file" << std::endl;
                return false;
            }
            net_ = cv::dnn::readNetFromDarknet(config_path_, model_path_);
        } else {
            std::cerr << "Unsupported model format" << std::endl;
            return false;
        }
        
        if (net_.empty()) {
            std::cerr << "Failed to load neural network" << std::endl;
            return false;
        }
        
        // 设置计算后端
        net_.setPreferableBackend(cv::dnn::DNN_BACKEND_OPENCV);
        net_.setPreferableTarget(cv::dnn::DNN_TARGET_CPU);
        
        // 获取输出层名称
        output_names_ = net_.getUnconnectedOutLayersNames();
        
        // 加载默认的COCO类别名称
        if (class_names_.empty()) {
            class_names_ = {
                "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck",
                "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench",
                "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra",
                "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
                "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove",
                "skateboard", "surfboard", "tennis racket", "bottle", "wine glass", "cup",
                "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich", "orange",
                "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
                "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse",
                "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink",
                "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier",
                "toothbrush"
            };
        }
        
        initialized_ = true;
        std::cout << "YOLO detector initialized successfully" << std::endl;
        return true;
        
    } catch (const cv::Exception& e) {
        std::cerr << "OpenCV exception in YOLO initialization: " << e.what() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Exception in YOLO initialization: " << e.what() << std::endl;
        return false;
    }
}

std::vector<DetectionResult> YOLODetector::detect(const cv::Mat& frame) {
    std::vector<DetectionResult> detections;
    
    if (!initialized_ || frame.empty()) {
        return detections;
    }
    
    try {
        // 创建blob
        cv::Mat blob;
        cv::dnn::blobFromImage(frame, blob, scale_factor_, 
                              cv::Size(input_width_, input_height_), 
                              mean_subtraction_, swap_rb_, false);
        
        // 设置输入
        net_.setInput(blob);
        
        // 前向传播
        std::vector<cv::Mat> outputs;
        net_.forward(outputs, output_names_);
        
        // 后处理
        postprocess(frame, outputs, detections);
        
    } catch (const cv::Exception& e) {
        std::cerr << "OpenCV exception in YOLO detection: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Exception in YOLO detection: " << e.what() << std::endl;
    }
    
    return detections;
}

void YOLODetector::postprocess(const cv::Mat& frame, const std::vector<cv::Mat>& outputs,
                              std::vector<DetectionResult>& detections) {
    std::vector<int> class_ids;
    std::vector<float> confidences;
    std::vector<cv::Rect> boxes;
    
    float x_factor = frame.cols / static_cast<float>(input_width_);
    float y_factor = frame.rows / static_cast<float>(input_height_);
    
    for (const auto& output : outputs) {
        const float* data = (float*)output.data;
        
        for (int i = 0; i < output.rows; ++i) {
            const float* row = data + i * output.cols;
            
            // 获取置信度分数
            cv::Mat scores = output.row(i).colRange(5, output.cols);
            cv::Point class_id_point;
            double max_class_score;
            minMaxLoc(scores, 0, &max_class_score, 0, &class_id_point);
            
            if (max_class_score > confidence_threshold_) {
                float cx = row[0];
                float cy = row[1];
                float w = row[2];
                float h = row[3];
                
                int left = static_cast<int>((cx - 0.5 * w) * x_factor);
                int top = static_cast<int>((cy - 0.5 * h) * y_factor);
                int width = static_cast<int>(w * x_factor);
                int height = static_cast<int>(h * y_factor);
                
                boxes.emplace_back(left, top, width, height);
                confidences.push_back(static_cast<float>(max_class_score));
                class_ids.push_back(class_id_point.x);
            }
        }
    }
    
    // 应用NMS
    std::vector<int> indices;
    cv::dnn::NMSBoxes(boxes, confidences, confidence_threshold_, nms_threshold_, indices);
    
    // 创建最终的检测结果
    for (int idx : indices) {
        cv::Rect box = boxes[idx];
        int class_id = class_ids[idx];
        float confidence = confidences[idx];
        
        std::string class_name = (class_id < class_names_.size()) ? 
                                class_names_[class_id] : "unknown";
        
        detections.emplace_back(box, confidence, class_id, class_name);
    }
}

bool YOLODetector::loadClassNames(const std::string& names_file) {
    std::ifstream file(names_file);
    if (!file.is_open()) {
        std::cerr << "Failed to open class names file: " << names_file << std::endl;
        return false;
    }
    
    class_names_.clear();
    std::string line;
    while (std::getline(file, line)) {
        class_names_.push_back(line);
    }
    
    std::cout << "Loaded " << class_names_.size() << " class names" << std::endl;
    return true;
}

void YOLODetector::cleanup() {
    if (initialized_) {
        net_ = cv::dnn::Net();
        initialized_ = false;
        std::cout << "YOLO detector cleaned up" << std::endl;
    }
}
