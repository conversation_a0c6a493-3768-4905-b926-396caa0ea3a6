#include "frame_processor.h"
#include <iostream>
#include <chrono>
#include <algorithm>

FrameProcessor::FrameProcessor(int num_threads, size_t input_queue_size, size_t output_queue_size)
    : running_(false), stop_requested_(false), 
      max_input_queue_size_(input_queue_size), 
      max_output_queue_size_(output_queue_size),
      num_threads_(num_threads) {
}

FrameProcessor::~FrameProcessor() {
    stop();
}

void FrameProcessor::addDetector(std::unique_ptr<DetectorBase> detector) {
    if (running_) {
        std::cerr << "Cannot add detector while processor is running" << std::endl;
        return;
    }
    
    if (detector) {
        std::cout << "Added detector: " << detector->getName() << std::endl;
        detectors_.push_back(std::move(detector));
    }
}

bool FrameProcessor::start() {
    if (running_) {
        std::cout << "Frame processor already running" << std::endl;
        return true;
    }
    
    if (detectors_.empty()) {
        std::cerr << "No detectors added" << std::endl;
        return false;
    }
    
    running_ = true;
    stop_requested_ = false;
    
    // 启动工作线程
    for (int i = 0; i < num_threads_; ++i) {
        worker_threads_.emplace_back(&FrameProcessor::workerThread, this, i);
    }
    
    std::cout << "Frame processor started with " << num_threads_ << " threads" << std::endl;
    return true;
}

void FrameProcessor::stop() {
    if (!running_) {
        return;
    }
    
    stop_requested_ = true;
    
    // 唤醒所有等待的线程
    input_cv_.notify_all();
    
    // 等待所有工作线程结束
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    
    worker_threads_.clear();
    running_ = false;
    
    // 清空队列
    {
        std::lock_guard<std::mutex> lock(input_mutex_);
        while (!input_queue_.empty()) {
            input_queue_.pop();
        }
    }
    
    {
        std::lock_guard<std::mutex> lock(output_mutex_);
        while (!output_queue_.empty()) {
            output_queue_.pop();
        }
    }
    
    std::cout << "Frame processor stopped" << std::endl;
}

bool FrameProcessor::submitFrame(const cv::Mat& frame, int frame_id, double timestamp) {
    if (!running_ || stop_requested_) {
        return false;
    }
    
    std::unique_lock<std::mutex> lock(input_mutex_);
    
    // 检查队列是否已满
    if (input_queue_.size() >= max_input_queue_size_) {
        return false; // 队列已满，丢弃帧
    }
    
    input_queue_.emplace(frame, frame_id, timestamp);
    lock.unlock();
    
    input_cv_.notify_one();
    return true;
}

bool FrameProcessor::getResult(FrameDetectionResult& result, int timeout_ms) {
    std::unique_lock<std::mutex> lock(output_mutex_);
    
    if (output_queue_.empty()) {
        if (timeout_ms <= 0) {
            return false;
        }
        
        auto timeout = std::chrono::milliseconds(timeout_ms);
        if (!output_cv_.wait_for(lock, timeout, [this] { return !output_queue_.empty() || stop_requested_; })) {
            return false;
        }
    }
    
    if (output_queue_.empty()) {
        return false;
    }
    
    result = std::move(output_queue_.front());
    output_queue_.pop();
    
    return true;
}

void FrameProcessor::setResultCallback(std::function<void(const FrameDetectionResult&)> callback) {
    result_callback_ = callback;
}

void FrameProcessor::workerThread(int thread_id) {
    std::cout << "Worker thread " << thread_id << " started" << std::endl;
    
    while (!stop_requested_) {
        FrameTask task(cv::Mat(), -1, 0.0);
        
        // 获取任务
        {
            std::unique_lock<std::mutex> lock(input_mutex_);
            
            if (input_queue_.empty()) {
                input_cv_.wait(lock, [this] { return !input_queue_.empty() || stop_requested_; });
            }
            
            if (stop_requested_) {
                break;
            }
            
            if (input_queue_.empty()) {
                continue;
            }
            
            task = std::move(input_queue_.front());
            input_queue_.pop();
        }
        
        // 处理帧
        FrameDetectionResult result = processFrame(task);
        
        // 存储结果
        {
            std::unique_lock<std::mutex> lock(output_mutex_);
            
            // 检查输出队列是否已满
            if (output_queue_.size() >= max_output_queue_size_) {
                output_queue_.pop(); // 丢弃最旧的结果
            }
            
            output_queue_.push(std::move(result));
        }
        
        output_cv_.notify_one();
        
        // 调用回调函数
        if (result_callback_) {
            result_callback_(result);
        }
    }
    
    std::cout << "Worker thread " << thread_id << " stopped" << std::endl;
}

FrameDetectionResult FrameProcessor::processFrame(const FrameTask& task) {
    FrameDetectionResult result;
    result.frame_id = task.frame_id;
    result.timestamp = task.timestamp;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 复制原始帧
    result.processed_frame = task.frame.clone();
    
    // 运行所有检测器
    for (const auto& detector : detectors_) {
        auto detections = detector->detect(task.frame);
        result.detections.insert(result.detections.end(), detections.begin(), detections.end());
    }
    
    // 绘制检测结果
    if (!result.detections.empty()) {
        result.processed_frame = drawDetections(result.processed_frame, result.detections);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    result.processing_time = duration.count();
    
    return result;
}

cv::Mat FrameProcessor::resizeFrame(const cv::Mat& frame, int target_width, int target_height, bool maintain_aspect_ratio) {
    if (frame.empty()) {
        return cv::Mat();
    }
    
    cv::Mat resized;
    
    if (!maintain_aspect_ratio) {
        cv::resize(frame, resized, cv::Size(target_width, target_height));
        return resized;
    }
    
    // 计算缩放比例
    double scale_x = static_cast<double>(target_width) / frame.cols;
    double scale_y = static_cast<double>(target_height) / frame.rows;
    double scale = std::min(scale_x, scale_y);
    
    int new_width = static_cast<int>(frame.cols * scale);
    int new_height = static_cast<int>(frame.rows * scale);
    
    cv::resize(frame, resized, cv::Size(new_width, new_height));
    
    // 添加黑边
    if (new_width != target_width || new_height != target_height) {
        cv::Mat padded = cv::Mat::zeros(target_height, target_width, frame.type());
        
        int x_offset = (target_width - new_width) / 2;
        int y_offset = (target_height - new_height) / 2;
        
        cv::Rect roi(x_offset, y_offset, new_width, new_height);
        resized.copyTo(padded(roi));
        
        return padded;
    }
    
    return resized;
}

cv::Mat FrameProcessor::drawDetections(const cv::Mat& frame, const std::vector<DetectionResult>& detections) {
    cv::Mat result = frame.clone();
    
    for (const auto& detection : detections) {
        // 绘制边界框
        cv::rectangle(result, detection.bbox, cv::Scalar(0, 255, 0), 2);
        
        // 绘制标签
        std::string label = detection.class_name + " " + 
                          std::to_string(static_cast<int>(detection.confidence * 100)) + "%";
        
        int baseline = 0;
        cv::Size text_size = cv::getTextSize(label, cv::FONT_HERSHEY_SIMPLEX, 0.5, 1, &baseline);
        
        cv::Point label_pos(detection.bbox.x, detection.bbox.y - 10);
        if (label_pos.y < text_size.height) {
            label_pos.y = detection.bbox.y + text_size.height + 10;
        }
        
        cv::rectangle(result, 
                     cv::Rect(label_pos.x, label_pos.y - text_size.height - baseline,
                             text_size.width, text_size.height + baseline),
                     cv::Scalar(0, 255, 0), -1);
        
        cv::putText(result, label, label_pos, cv::FONT_HERSHEY_SIMPLEX, 0.5, 
                   cv::Scalar(0, 0, 0), 1);
    }
    
    return result;
}
