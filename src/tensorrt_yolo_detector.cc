#include "tensorrt_yolo_detector.h"
#include <iostream>
#include <fstream>
#include <stdexcept>
#include <iomanip>
#include "trtyolo.hpp"

TensorRTYOLODetector::TensorRTYOLODetector(const std::string& model_path, const std::string& config_path)
    : DetectorBase("TensorRTYOLO"), model_path_(model_path), config_path_(config_path),
      initialized_(false), input_size_(640, 640), enable_swap_rb_(true) {
    
    // 初始化默认类别名称
    class_names_ = {
        "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck",
        "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench",
        "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra",
        "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
        "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove",
        "skateboard", "surfboard", "tennis racket", "bottle", "wine glass", "cup",
        "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich", "orange",
        "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
        "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse",
        "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink",
        "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier",
        "toothbrush"
    };
}

TensorRTYOLODetector::~TensorRTYOLODetector() {
    cleanup();
}

bool TensorRTYOLODetector::initialize() {
    try {
        std::cout << "Initializing TensorRT YOLO Detector..." << std::endl;
        
        // 检查模型文件是否存在
        std::ifstream model_file(model_path_);
        if (!model_file.good()) {
            std::cerr << "Model file not found: " << model_path_ << std::endl;
            std::cout << "Using mock implementation for demonstration" << std::endl;
        }
        
        // 创建推理选项
        option_ = std::make_unique<trtyolo::InferOption>();
        
        // 设置预处理参数
        if (enable_swap_rb_) {
            option_->enableSwapRB();
        }
        
        if (!normalize_mean_.empty() && !normalize_std_.empty()) {
            option_->setNormalizeParams(normalize_mean_, normalize_std_);
        }
        
        // 创建检测器
        detector_ = std::make_unique<trtyolo::DetectModel>(model_path_, *option_);
        
        // 加载类别名称（如果有配置文件）
        if (!config_path_.empty()) {
            loadClassNames(config_path_);
        }
        
        initialized_ = true;
        std::cout << "TensorRT YOLO Detector initialized successfully" << std::endl;
        std::cout << "Model: " << model_path_ << std::endl;
        std::cout << "Input size: " << input_size_.width << "x" << input_size_.height << std::endl;
        std::cout << "Classes: " << class_names_.size() << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize TensorRT YOLO Detector: " << e.what() << std::endl;
        return false;
    }
}

std::vector<DetectionResult> TensorRTYOLODetector::detect(const cv::Mat& frame) {
    std::vector<DetectionResult> results;
    
    if (!initialized_ || !detector_) {
        std::cerr << "Detector not initialized" << std::endl;
        return results;
    }
    
    if (frame.empty()) {
        std::cerr << "Empty input frame" << std::endl;
        return results;
    }
    
    try {
        // 开始计时
        auto start_time = std::chrono::high_resolution_clock::now();

        // 创建TensorRT图像包装器
        auto trt_image = createTRTImage(frame);

        // 执行推理
        trtyolo::DetectRes trt_result = detector_->predict(*trt_image);

        // 结束计时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        double inference_time_ms = duration.count() / 1000.0;

        // 更新性能统计
        updatePerformanceStats(inference_time_ms);

        // 转换结果格式
        results = convertResults(trt_result, cv::Size(frame.cols, frame.rows));

        // 输出计时信息
        std::cout << "TensorRT inference time: " << std::fixed << std::setprecision(2)
                 << inference_time_ms << " ms" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Detection failed: " << e.what() << std::endl;
    }
    
    return results;
}

void TensorRTYOLODetector::cleanup() {
    if (detector_) {
        detector_.reset();
    }
    if (option_) {
        option_.reset();
    }
    initialized_ = false;
    std::cout << "TensorRT YOLO Detector cleaned up" << std::endl;
}

void TensorRTYOLODetector::setPreprocessParams(bool enable_swap_rb,
                                             const std::vector<float>& mean,
                                             const std::vector<float>& std) {
    enable_swap_rb_ = enable_swap_rb;
    normalize_mean_ = mean;
    normalize_std_ = std;
    
    // 如果已经初始化，需要重新设置参数
    if (initialized_ && option_) {
        if (enable_swap_rb_) {
            option_->enableSwapRB();
        }
        if (!mean.empty() && !std.empty()) {
            option_->setNormalizeParams(mean, std);
        }
    }
}

std::unique_ptr<TensorRTYOLODetector> TensorRTYOLODetector::clone() const {
    auto cloned = std::make_unique<TensorRTYOLODetector>(model_path_, config_path_);
    cloned->setPreprocessParams(enable_swap_rb_, normalize_mean_, normalize_std_);
    cloned->setConfidenceThreshold(getConfidenceThreshold());
    cloned->setNMSThreshold(getNMSThreshold());
    
    if (cloned->initialize()) {
        return cloned;
    }
    return nullptr;
}

bool TensorRTYOLODetector::loadClassNames(const std::string& config_path) {
    std::ifstream file(config_path);
    if (!file.is_open()) {
        std::cerr << "Cannot open class names file: " << config_path << std::endl;
        return false;
    }
    
    class_names_.clear();
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            class_names_.push_back(line);
        }
    }
    
    std::cout << "Loaded " << class_names_.size() << " class names from " << config_path << std::endl;
    return true;
}

std::vector<DetectionResult> TensorRTYOLODetector::convertResults(const trtyolo::DetectRes& trt_result,
                                                                const cv::Size& original_size) const {
    std::vector<DetectionResult> results;

    // 遍历所有检测结果
    for (int i = 0; i < trt_result.num; ++i) {
        // 过滤低置信度检测
        if (trt_result.scores[i] < confidence_threshold_) {
            continue;
        }

        // 获取边界框
        const auto& box = trt_result.boxes[i];

        // 转换边界框坐标 (left, top, right, bottom) -> (x, y, width, height)
        cv::Rect bbox(
            static_cast<int>(box.left),
            static_cast<int>(box.top),
            static_cast<int>(box.right - box.left),
            static_cast<int>(box.bottom - box.top)
        );

        // 确保边界框在图像范围内
        bbox &= cv::Rect(0, 0, original_size.width, original_size.height);

        // 获取类别名称
        std::string class_name = "unknown";
        int class_id = trt_result.classes[i];
        if (class_id >= 0 && class_id < static_cast<int>(class_names_.size())) {
            class_name = class_names_[class_id];
        }

        results.emplace_back(bbox, trt_result.scores[i], class_id, class_name);
    }

    return results;
}

std::unique_ptr<trtyolo::Image> TensorRTYOLODetector::createTRTImage(const cv::Mat& cv_image) const {
    return std::make_unique<trtyolo::Image>(
        cv_image.data,
        cv_image.cols,
        cv_image.rows
    );
}

void TensorRTYOLODetector::updatePerformanceStats(double inference_time_ms) const {
    std::lock_guard<std::mutex> lock(perf_mutex_);

    perf_stats_.total_inferences++;
    perf_stats_.total_time_ms += inference_time_ms;
    perf_stats_.avg_inference_time_ms = perf_stats_.total_time_ms / perf_stats_.total_inferences;

    if (perf_stats_.total_inferences == 1) {
        perf_stats_.min_inference_time_ms = inference_time_ms;
        perf_stats_.max_inference_time_ms = inference_time_ms;
    } else {
        perf_stats_.min_inference_time_ms = std::min(perf_stats_.min_inference_time_ms, inference_time_ms);
        perf_stats_.max_inference_time_ms = std::max(perf_stats_.max_inference_time_ms, inference_time_ms);
    }
}

TensorRTYOLODetector::PerformanceStats TensorRTYOLODetector::getPerformanceStats() const {
    std::lock_guard<std::mutex> lock(perf_mutex_);
    return perf_stats_;
}

void TensorRTYOLODetector::resetPerformanceStats() {
    std::lock_guard<std::mutex> lock(perf_mutex_);
    perf_stats_ = PerformanceStats{};
}
