#pragma once

#include "detector_base.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>

/**
 * 帧处理任务结构体
 */
struct FrameTask {
    cv::Mat frame;
    int frame_id;
    double timestamp;
    
    FrameTask(const cv::Mat& f, int id, double ts) 
        : frame(f.clone()), frame_id(id), timestamp(ts) {}
};

/**
 * 帧检测结果结构体
 */
struct FrameDetectionResult {
    cv::Mat processed_frame;
    std::vector<DetectionResult> detections;
    int frame_id;
    double timestamp;
    double processing_time;
    
    FrameDetectionResult() : frame_id(-1), timestamp(0.0), processing_time(0.0) {}
};

/**
 * 多线程帧处理器
 */
class FrameProcessor {
public:
    FrameProcessor(int num_threads = 4, size_t input_queue_size = 10, size_t output_queue_size = 10);
    ~FrameProcessor();
    
    // 添加检测器
    void addDetector(std::unique_ptr<DetectorBase> detector);
    
    // 启动处理
    bool start();
    
    // 停止处理
    void stop();
    
    // 提交帧进行处理
    bool submitFrame(const cv::Mat& frame, int frame_id, double timestamp);
    
    // 获取处理结果
    bool getResult(FrameDetectionResult& result, int timeout_ms = 1000);
    
    // 获取队列大小
    size_t getInputQueueSize() const { return input_queue_.size(); }
    size_t getOutputQueueSize() const { return output_queue_.size(); }
    
    // 设置结果回调函数
    void setResultCallback(std::function<void(const FrameDetectionResult&)> callback);
    
    // 静态工具函数
    static cv::Mat resizeFrame(const cv::Mat& frame, int target_width, int target_height, bool maintain_aspect_ratio = true);
    static cv::Mat drawDetections(const cv::Mat& frame, const std::vector<DetectionResult>& detections);

private:
    // 工作线程函数
    void workerThread(int thread_id);
    
    // 处理单帧
    FrameDetectionResult processFrame(const FrameTask& task);
    
    // 成员变量
    std::vector<std::unique_ptr<DetectorBase>> detectors_;
    std::vector<std::thread> worker_threads_;
    
    std::queue<FrameTask> input_queue_;
    std::queue<FrameDetectionResult> output_queue_;
    
    mutable std::mutex input_mutex_;
    mutable std::mutex output_mutex_;
    
    std::condition_variable input_cv_;
    std::condition_variable output_cv_;
    
    std::atomic<bool> running_;
    std::atomic<bool> stop_requested_;
    
    size_t max_input_queue_size_;
    size_t max_output_queue_size_;
    int num_threads_;
    
    std::function<void(const FrameDetectionResult&)> result_callback_;
};
