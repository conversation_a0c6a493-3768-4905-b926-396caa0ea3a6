#pragma once

#include <string>
#include <vector>
#include <map>

/**
 * 编码配置结构体
 */
struct EncodingConfig {
    double keyframe_interval = 2.0;    // 关键帧间隔（秒）
    bool force_keyframes = false;      // 是否强制关键帧
    std::string preset = "medium";     // 编码预设
    std::string profile = "main";      // H.264配置文件
    std::string level = "4.0";         // H.264级别
    bool zero_latency = false;         // 零延迟模式
    int refs = 3;                      // 参考帧数量
    int bframes = 3;                   // B帧数量
};

/**
 * 流配置结构体
 */
struct StreamConfig {
    std::string input_stream;          // 输入流地址
    std::string output_stream;         // 输出流地址
    int output_width = 1920;           // 输出宽度
    int output_height = 1080;          // 输出高度
    double fps = 30.0;                 // 帧率
    bool maintain_aspect_ratio = true; // 保持宽高比
    EncodingConfig encoding;           // 编码配置
};

/**
 * 检测器配置结构体
 */
struct DetectorConfig {
    std::string type;                  // 检测器类型
    std::string model_path;            // 模型路径
    std::string config_path;           // 配置文件路径
    std::string names_path;            // 类别名称文件路径
    float confidence_threshold = 0.5f; // 置信度阈值
    float nms_threshold = 0.4f;        // NMS阈值
};

/**
 * 处理器配置结构体
 */
struct ProcessorConfig {
    int num_threads = 4;               // 处理线程数
    size_t input_queue_size = 10;      // 输入队列大小
    size_t output_queue_size = 10;     // 输出队列大小
    std::map<std::string, DetectorConfig> detectors; // 检测器配置
};

/**
 * 应用配置结构体
 */
struct AppConfig {
    bool enable_detection = true;      // 是否启用目标检测
    bool show_detection_info = true;   // 是否显示检测信息
};

/**
 * 主配置结构体
 */
struct Config {
    StreamConfig stream;               // 流配置
    ProcessorConfig processor;         // 处理器配置
    AppConfig app;                     // 应用配置
};

/**
 * 配置加载器类
 */
class ConfigLoader {
public:
    static bool loadFromFile(const std::string& filename, Config& config);
    static bool saveToFile(const std::string& filename, const Config& config);
    static Config getDefaultConfig();
};
