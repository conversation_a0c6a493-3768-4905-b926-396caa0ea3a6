#pragma once

#include "detector_base.h"
#include <opencv2/dnn.hpp>
#include <fstream>

class YOLODetector : public DetectorBase {
public:
    YOLODetector(const std::string& model_path, const std::string& config_path = "");
    ~YOLODetector() override;
    
    bool initialize() override;
    std::vector<DetectionResult> detect(const cv::Mat& frame) override;
    void cleanup() override;
    
    // YOLO特有的设置
    void setInputSize(int width, int height) { input_width_ = width; input_height_ = height; }
    void setScaleFactor(double scale) { scale_factor_ = scale; }
    void setMeanSubtraction(const cv::Scalar& mean) { mean_subtraction_ = mean; }
    void setSwapRB(bool swap) { swap_rb_ = swap; }
    
    // 加载类别名称
    bool loadClassNames(const std::string& names_file);

private:
    void postprocess(const cv::Mat& frame, const std::vector<cv::Mat>& outputs, 
                    std::vector<DetectionResult>& detections);
    
    std::string model_path_;
    std::string config_path_;
    cv::dnn::Net net_;
    
    // YOLO参数
    int input_width_ = 416;
    int input_height_ = 416;
    double scale_factor_ = 1.0 / 255.0;
    cv::Scalar mean_subtraction_ = cv::Scalar(0, 0, 0);
    bool swap_rb_ = true;
    
    // 类别名称
    std::vector<std::string> class_names_;
    
    // 输出层名称
    std::vector<std::string> output_names_;
    
    bool initialized_ = false;
};
