#pragma once

#include <string>
#include <map>
#include <vector>
#include <variant>
#include <memory>

/**
 * 简单的YAML解析器
 * 支持基本的YAML格式解析，包括嵌套对象和数组
 */
class SimpleYAML {
public:
    // YAML值类型
    using Value = std::variant<std::string, int, double, bool, std::map<std::string, std::shared_ptr<SimpleYAML>>, std::vector<std::shared_ptr<SimpleYAML>>>;
    
    SimpleYAML() = default;
    explicit SimpleYAML(const Value& value) : value_(value) {}
    
    // 从文件加载
    static std::shared_ptr<SimpleYAML> loadFromFile(const std::string& filename);
    
    // 从字符串解析
    static std::shared_ptr<SimpleYAML> parse(const std::string& content);
    
    // 保存到文件
    bool saveToFile(const std::string& filename) const;
    
    // 转换为字符串
    std::string toString(int indent = 0) const;
    
    // 访问器
    std::shared_ptr<SimpleYAML> operator[](const std::string& key) const;
    std::shared_ptr<SimpleYAML> operator[](size_t index) const;
    
    // 类型检查
    bool isString() const;
    bool isInt() const;
    bool isDouble() const;
    bool isBool() const;
    bool isMap() const;
    bool isArray() const;
    bool isNull() const;
    
    // 值获取
    std::string asString(const std::string& defaultValue = "") const;
    int asInt(int defaultValue = 0) const;
    double asDouble(double defaultValue = 0.0) const;
    bool asBool(bool defaultValue = false) const;
    
    // 设置值
    void set(const std::string& key, std::shared_ptr<SimpleYAML> value);
    void set(const std::string& key, const std::string& value);
    void set(const std::string& key, int value);
    void set(const std::string& key, double value);
    void set(const std::string& key, bool value);
    
    // 添加到数组
    void push(std::shared_ptr<SimpleYAML> value);
    
    // 获取键列表
    std::vector<std::string> getKeys() const;
    
    // 获取数组大小
    size_t size() const;

private:
    Value value_;
    
    // 解析辅助函数
    static std::shared_ptr<SimpleYAML> parseValue(const std::string& line, size_t& pos);
    static std::shared_ptr<SimpleYAML> parseMap(const std::vector<std::string>& lines, size_t& lineIndex, int baseIndent);
    static std::shared_ptr<SimpleYAML> parseArray(const std::vector<std::string>& lines, size_t& lineIndex, int baseIndent);
    static int getIndentLevel(const std::string& line);
    static std::string trim(const std::string& str);
    static std::vector<std::string> split(const std::string& str, char delimiter);
};
