#pragma once

#include "detector_base.h"
#include "trtyolo.hpp"
#include <memory>
#include <vector>
#include <string>
#include <opencv2/opencv.hpp>

/**
 * TensorRT YOLO检测器实现类
 * 基于TensorRT优化的YOLO模型进行目标检测
 */
class TensorRTYOLODetector : public DetectorBase {
public:
    /**
     * 构造函数
     * @param model_path TensorRT引擎文件路径 (.engine)
     * @param config_path 配置文件路径（可选）
     */
    TensorRTYOLODetector(const std::string& model_path, const std::string& config_path = "");
    
    /**
     * 析构函数
     */
    virtual ~TensorRTYOLODetector();
    
    /**
     * 初始化检测器
     * @return 成功返回true，失败返回false
     */
    bool initialize() override;
    
    /**
     * 执行目标检测
     * @param frame 输入图像
     * @return 检测结果列表
     */
    std::vector<DetectionResult> detect(const cv::Mat& frame) override;
    
    /**
     * 清理资源
     */
    void cleanup() override;
    
    /**
     * 设置图像预处理参数
     * @param enable_swap_rb 是否启用BGR->RGB转换
     * @param mean 归一化均值
     * @param std 归一化标准差
     */
    void setPreprocessParams(bool enable_swap_rb = true, 
                           const std::vector<float>& mean = {},
                           const std::vector<float>& std = {});
    
    /**
     * 获取模型信息
     */
    cv::Size getInputSize() const { return input_size_; }
    bool isInitialized() const { return initialized_; }
    
    /**
     * 创建检测器的独立副本
     * @return 新的检测器实例
     */
    std::unique_ptr<TensorRTYOLODetector> clone() const;

private:
    // 私有成员变量
    std::string model_path_;                    // 模型文件路径
    std::string config_path_;                   // 配置文件路径
    std::unique_ptr<trtyolo::DetectModel> detector_;  // TensorRT检测器实例
    std::unique_ptr<trtyolo::InferOption> option_;    // 推理选项
    
    bool initialized_;                          // 初始化状态
    cv::Size input_size_;                       // 模型输入尺寸
    
    // 预处理参数
    bool enable_swap_rb_;                       // BGR->RGB转换
    std::vector<float> normalize_mean_;         // 归一化均值
    std::vector<float> normalize_std_;          // 归一化标准差
    
    // 类别名称映射（可从配置文件加载）
    std::vector<std::string> class_names_;
    
    /**
     * 加载类别名称
     * @param config_path 配置文件路径
     * @return 成功返回true
     */
    bool loadClassNames(const std::string& config_path);
    
    /**
     * 转换TensorRT检测结果到系统格式
     * @param trt_result TensorRT检测结果
     * @param original_size 原始图像尺寸
     * @return 系统检测结果
     */
    std::vector<DetectionResult> convertResults(const trtyolo::DetectRes& trt_result, 
                                               const cv::Size& original_size) const;
    
    /**
     * 创建TensorRT图像包装器
     * @param cv_image OpenCV图像
     * @return TensorRT图像对象
     */
    std::unique_ptr<trtyolo::Image> createTRTImage(const cv::Mat& cv_image) const;
};
