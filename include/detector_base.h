#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include <memory>

/**
 * 检测结果结构体
 */
struct DetectionResult {
    cv::Rect bbox;          // 边界框
    float confidence;       // 置信度
    int class_id;          // 类别ID
    std::string class_name; // 类别名称
    
    DetectionResult(const cv::Rect& bbox, float confidence, int class_id, const std::string& class_name)
        : bbox(bbox), confidence(confidence), class_id(class_id), class_name(class_name) {}
};

/**
 * 检测器基类
 */
class DetectorBase {
public:
    DetectorBase(const std::string& name) : name_(name), confidence_threshold_(0.5f), nms_threshold_(0.4f) {}
    virtual ~DetectorBase() = default;
    
    // 纯虚函数，子类必须实现
    virtual bool initialize() = 0;
    virtual std::vector<DetectionResult> detect(const cv::Mat& frame) = 0;
    virtual void cleanup() = 0;
    
    // 设置参数
    void setConfidenceThreshold(float threshold) { confidence_threshold_ = threshold; }
    void setNMSThreshold(float threshold) { nms_threshold_ = threshold; }
    
    // 获取参数
    float getConfidenceThreshold() const { return confidence_threshold_; }
    float getNMSThreshold() const { return nms_threshold_; }
    std::string getName() const { return name_; }

protected:
    std::string name_;
    float confidence_threshold_;
    float nms_threshold_;
};

/**
 * 检测器工厂类
 */
class DetectorFactory {
public:
    static std::unique_ptr<DetectorBase> createDetector(
        const std::string& type,
        const std::string& model_path,
        const std::string& config_path = "",
        const std::string& names_path = ""
    );
};
