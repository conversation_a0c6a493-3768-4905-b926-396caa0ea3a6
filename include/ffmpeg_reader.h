#pragma once

#include <string>
#include <opencv2/opencv.hpp>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/imgutils.h>
}

/**
 * FFmpeg视频流读取器
 * 支持RTMP、HTTP、本地文件等多种输入源
 */
class FFmpegReader {
public:
    FFmpegReader();
    ~FFmpegReader();
    
    // 打开输入流
    bool open(const std::string& url);
    
    // 读取一帧
    bool readFrame(cv::Mat& frame);
    
    // 获取视频信息
    int getWidth() const { return width_; }
    int getHeight() const { return height_; }
    double getFPS() const { return fps_; }
    
    // 分辨率变化检测
    bool hasResolutionChanged() const { return resolution_changed_; }
    void resetResolutionFlag() { resolution_changed_ = false; }
    
    // 关闭流
    void close();

    // 检查是否已打开
    bool isOpened() const { return format_context_ != nullptr; }
    
    // 中断读取操作
    void interrupt() { should_interrupt_ = true; }

private:
    // FFmpeg相关
    AVFormatContext* format_context_;
    AVCodecContext* codec_context_;
    const AVCodec* codec_;
    AVFrame* frame_;
    AVFrame* frame_bgr_;
    AVPacket* packet_;
    SwsContext* sws_context_;
    
    int video_stream_index_;
    int width_;
    int height_;
    double fps_;
    
    // 分辨率变化检测
    bool resolution_changed_;
    int last_width_;
    int last_height_;
    
    // 中断控制
    volatile bool should_interrupt_;

    // 内部方法
    bool initializeDecoder();
    bool reinitializeScaler(int new_width, int new_height);
    void cleanup();
    
    // FFmpeg中断回调
    static int interruptCallback(void* ctx);
};
