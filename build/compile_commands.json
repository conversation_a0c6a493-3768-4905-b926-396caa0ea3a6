[{"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/main.cc.o -c /home/<USER>/cplus/videopush/src/main.cc", "file": "/home/<USER>/cplus/videopush/src/main.cc", "output": "CMakeFiles/rtmp_test.dir/src/main.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/detector_base.cc.o -c /home/<USER>/cplus/videopush/src/detector_base.cc", "file": "/home/<USER>/cplus/videopush/src/detector_base.cc", "output": "CMakeFiles/rtmp_test.dir/src/detector_base.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/frame_processor.cc.o -c /home/<USER>/cplus/videopush/src/frame_processor.cc", "file": "/home/<USER>/cplus/videopush/src/frame_processor.cc", "output": "CMakeFiles/rtmp_test.dir/src/frame_processor.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/yolo_detector.cc.o -c /home/<USER>/cplus/videopush/src/yolo_detector.cc", "file": "/home/<USER>/cplus/videopush/src/yolo_detector.cc", "output": "CMakeFiles/rtmp_test.dir/src/yolo_detector.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/tensorrt_yolo_detector.cc.o -c /home/<USER>/cplus/videopush/src/tensorrt_yolo_detector.cc", "file": "/home/<USER>/cplus/videopush/src/tensorrt_yolo_detector.cc", "output": "CMakeFiles/rtmp_test.dir/src/tensorrt_yolo_detector.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/detection_config.cc.o -c /home/<USER>/cplus/videopush/src/detection_config.cc", "file": "/home/<USER>/cplus/videopush/src/detection_config.cc", "output": "CMakeFiles/rtmp_test.dir/src/detection_config.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/simple_yaml.cc.o -c /home/<USER>/cplus/videopush/src/simple_yaml.cc", "file": "/home/<USER>/cplus/videopush/src/simple_yaml.cc", "output": "CMakeFiles/rtmp_test.dir/src/simple_yaml.cc.o"}, {"directory": "/home/<USER>/cplus/videopush/build", "command": "/usr/bin/c++ -DDEBUG -I/usr/local/tensorrt-yolo/include -I/home/<USER>/cplus/videopush/include -isystem /usr/include/opencv4 -g -std=gnu++20 -o CMakeFiles/rtmp_test.dir/src/ffmpeg_reader.cc.o -c /home/<USER>/cplus/videopush/src/ffmpeg_reader.cc", "file": "/home/<USER>/cplus/videopush/src/ffmpeg_reader.cc", "output": "CMakeFiles/rtmp_test.dir/src/ffmpeg_reader.cc.o"}]