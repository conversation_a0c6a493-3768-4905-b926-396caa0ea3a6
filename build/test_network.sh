#!/bin/bash

echo "=== Network Performance Test ==="
echo ""

# 测试到RTMP服务器的连接
echo "1. Testing connection to RTMP server..."
RTMP_HOST="*************"
RTMP_PORT="19403"

ping -c 3 $RTMP_HOST
echo ""

# 测试端口连通性
echo "2. Testing RTMP port connectivity..."
timeout 5 telnet $RTMP_HOST $RTMP_PORT 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ RTMP port $RTMP_PORT is accessible"
else
    echo "❌ RTMP port $RTMP_PORT is not accessible or slow"
fi
echo ""

# 简单的带宽测试
echo "3. Testing upload speed with small file..."
echo "Creating test file..."
dd if=/dev/zero of=test_upload.dat bs=1M count=1 2>/dev/null

echo "Testing upload speed..."
start_time=$(date +%s.%N)
curl -X POST -F "file=@test_upload.dat" httpbin.org/post > /dev/null 2>&1
end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
speed=$(echo "scale=2; 1 / $duration" | bc)

echo "Upload speed: ~${speed} MB/s"
rm -f test_upload.dat

echo ""
echo "=== Recommendations ==="
echo "For 0.5x streaming speed, try:"
echo "- Reduce bitrate to 200-300kbps"
echo "- Use 320x240 resolution"
echo "- Check if other applications are using bandwidth"
