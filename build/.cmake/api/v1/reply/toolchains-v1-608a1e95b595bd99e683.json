{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/13/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/13", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["gcc", "gcc_s", "c", "gcc", "gcc_s"]}, "path": "/usr/bin/gcc", "version": "13.3.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["/usr/local/TensorRT-10.12.0.36/include", "/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/gcc/x86_64-linux-gnu/13/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "linkDirectories": ["/usr/lib/gcc/x86_64-linux-gnu/13", "/usr/lib/x86_64-linux-gnu", "/usr/lib", "/lib/x86_64-linux-gnu", "/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc_s", "gcc", "c", "gcc_s", "gcc"]}, "path": "/usr/bin/g++", "version": "13.3.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}