{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "rtmp_test", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "rtmp_test::@6890427a1f51a3e7e1df", "jsonFile": "target-rtmp_test-Debug-cd0e65de179c8de229bf.json", "name": "rtmp_test", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/cplus/videopush/build", "source": "/home/<USER>/cplus/videopush"}, "version": {"major": 2, "minor": 6}}