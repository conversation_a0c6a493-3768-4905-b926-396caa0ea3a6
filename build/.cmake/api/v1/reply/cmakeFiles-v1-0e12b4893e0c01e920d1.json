{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeUnixFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/<PERSON>-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Cray<PERSON><PERSON>-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Cray<PERSON><PERSON>-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/usr/lib/x86_64-linux-gnu/cmake/opencv4/OpenCVModules-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/cplus/videopush/build", "source": "/home/<USER>/cplus/videopush"}, "version": {"major": 1, "minor": 0}}