{"artifacts": [{"path": "rtmp_test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 59, "parent": 0}, {"command": 1, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 11, "parent": 0}, {"command": 2, "file": 0, "line": 69, "parent": 0}, {"command": 3, "file": 0, "line": 50, "parent": 0}, {"command": 4, "file": 0, "line": 14, "parent": 0}, {"command": 4, "file": 0, "line": 33, "parent": 0}, {"command": 4, "file": 0, "line": 13, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++20"}], "defines": [{"backtrace": 5, "define": "DEBUG"}], "includes": [{"backtrace": 6, "path": "/usr/local/tensorrt-yolo/include"}, {"backtrace": 7, "path": "/home/<USER>/cplus/videopush/include"}, {"backtrace": 8, "isSystem": true, "path": "/usr/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "id": "rtmp_test::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-L/usr/local/lib", "role": "libraryPath"}, {"backtrace": 3, "fragment": "-L/usr/local/tensorrt-yolo/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/lib:/usr/local/tensorrt-yolo/lib", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_cvv.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "-lavformat", "role": "libraries"}, {"backtrace": 4, "fragment": "-lavcodec", "role": "libraries"}, {"backtrace": 4, "fragment": "-lavu<PERSON>", "role": "libraries"}, {"backtrace": 4, "fragment": "-lswscale", "role": "libraries"}, {"backtrace": 4, "fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.6.0", "role": "libraries"}], "language": "CXX"}, "name": "rtmp_test", "nameOnDisk": "rtmp_test", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/detector_base.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/frame_processor.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/yolo_detector.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/tensorrt_yolo_detector.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/detection_config.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/simple_yaml.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/ffmpeg_reader.cc", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}