# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/cplus/videopush

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/cplus/videopush/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/cplus/videopush/build/CMakeFiles /home/<USER>/cplus/videopush/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/cplus/videopush/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named rtmp_test

# Build rule for target.
rtmp_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rtmp_test
.PHONY : rtmp_test

# fast build rule for target.
rtmp_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/build
.PHONY : rtmp_test/fast

src/detection_config.o: src/detection_config.cc.o
.PHONY : src/detection_config.o

# target to build an object file
src/detection_config.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detection_config.cc.o
.PHONY : src/detection_config.cc.o

src/detection_config.i: src/detection_config.cc.i
.PHONY : src/detection_config.i

# target to preprocess a source file
src/detection_config.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detection_config.cc.i
.PHONY : src/detection_config.cc.i

src/detection_config.s: src/detection_config.cc.s
.PHONY : src/detection_config.s

# target to generate assembly for a file
src/detection_config.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detection_config.cc.s
.PHONY : src/detection_config.cc.s

src/detector_base.o: src/detector_base.cc.o
.PHONY : src/detector_base.o

# target to build an object file
src/detector_base.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detector_base.cc.o
.PHONY : src/detector_base.cc.o

src/detector_base.i: src/detector_base.cc.i
.PHONY : src/detector_base.i

# target to preprocess a source file
src/detector_base.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detector_base.cc.i
.PHONY : src/detector_base.cc.i

src/detector_base.s: src/detector_base.cc.s
.PHONY : src/detector_base.s

# target to generate assembly for a file
src/detector_base.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/detector_base.cc.s
.PHONY : src/detector_base.cc.s

src/ffmpeg_reader.o: src/ffmpeg_reader.cc.o
.PHONY : src/ffmpeg_reader.o

# target to build an object file
src/ffmpeg_reader.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/ffmpeg_reader.cc.o
.PHONY : src/ffmpeg_reader.cc.o

src/ffmpeg_reader.i: src/ffmpeg_reader.cc.i
.PHONY : src/ffmpeg_reader.i

# target to preprocess a source file
src/ffmpeg_reader.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/ffmpeg_reader.cc.i
.PHONY : src/ffmpeg_reader.cc.i

src/ffmpeg_reader.s: src/ffmpeg_reader.cc.s
.PHONY : src/ffmpeg_reader.s

# target to generate assembly for a file
src/ffmpeg_reader.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/ffmpeg_reader.cc.s
.PHONY : src/ffmpeg_reader.cc.s

src/frame_processor.o: src/frame_processor.cc.o
.PHONY : src/frame_processor.o

# target to build an object file
src/frame_processor.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/frame_processor.cc.o
.PHONY : src/frame_processor.cc.o

src/frame_processor.i: src/frame_processor.cc.i
.PHONY : src/frame_processor.i

# target to preprocess a source file
src/frame_processor.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/frame_processor.cc.i
.PHONY : src/frame_processor.cc.i

src/frame_processor.s: src/frame_processor.cc.s
.PHONY : src/frame_processor.s

# target to generate assembly for a file
src/frame_processor.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/frame_processor.cc.s
.PHONY : src/frame_processor.cc.s

src/main.o: src/main.cc.o
.PHONY : src/main.o

# target to build an object file
src/main.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/main.cc.o
.PHONY : src/main.cc.o

src/main.i: src/main.cc.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/main.cc.i
.PHONY : src/main.cc.i

src/main.s: src/main.cc.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/main.cc.s
.PHONY : src/main.cc.s

src/simple_yaml.o: src/simple_yaml.cc.o
.PHONY : src/simple_yaml.o

# target to build an object file
src/simple_yaml.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/simple_yaml.cc.o
.PHONY : src/simple_yaml.cc.o

src/simple_yaml.i: src/simple_yaml.cc.i
.PHONY : src/simple_yaml.i

# target to preprocess a source file
src/simple_yaml.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/simple_yaml.cc.i
.PHONY : src/simple_yaml.cc.i

src/simple_yaml.s: src/simple_yaml.cc.s
.PHONY : src/simple_yaml.s

# target to generate assembly for a file
src/simple_yaml.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/simple_yaml.cc.s
.PHONY : src/simple_yaml.cc.s

src/tensorrt_yolo_detector.o: src/tensorrt_yolo_detector.cc.o
.PHONY : src/tensorrt_yolo_detector.o

# target to build an object file
src/tensorrt_yolo_detector.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/tensorrt_yolo_detector.cc.o
.PHONY : src/tensorrt_yolo_detector.cc.o

src/tensorrt_yolo_detector.i: src/tensorrt_yolo_detector.cc.i
.PHONY : src/tensorrt_yolo_detector.i

# target to preprocess a source file
src/tensorrt_yolo_detector.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/tensorrt_yolo_detector.cc.i
.PHONY : src/tensorrt_yolo_detector.cc.i

src/tensorrt_yolo_detector.s: src/tensorrt_yolo_detector.cc.s
.PHONY : src/tensorrt_yolo_detector.s

# target to generate assembly for a file
src/tensorrt_yolo_detector.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/tensorrt_yolo_detector.cc.s
.PHONY : src/tensorrt_yolo_detector.cc.s

src/yolo_detector.o: src/yolo_detector.cc.o
.PHONY : src/yolo_detector.o

# target to build an object file
src/yolo_detector.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/yolo_detector.cc.o
.PHONY : src/yolo_detector.cc.o

src/yolo_detector.i: src/yolo_detector.cc.i
.PHONY : src/yolo_detector.i

# target to preprocess a source file
src/yolo_detector.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/yolo_detector.cc.i
.PHONY : src/yolo_detector.cc.i

src/yolo_detector.s: src/yolo_detector.cc.s
.PHONY : src/yolo_detector.s

# target to generate assembly for a file
src/yolo_detector.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rtmp_test.dir/build.make CMakeFiles/rtmp_test.dir/src/yolo_detector.cc.s
.PHONY : src/yolo_detector.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... rtmp_test"
	@echo "... src/detection_config.o"
	@echo "... src/detection_config.i"
	@echo "... src/detection_config.s"
	@echo "... src/detector_base.o"
	@echo "... src/detector_base.i"
	@echo "... src/detector_base.s"
	@echo "... src/ffmpeg_reader.o"
	@echo "... src/ffmpeg_reader.i"
	@echo "... src/ffmpeg_reader.s"
	@echo "... src/frame_processor.o"
	@echo "... src/frame_processor.i"
	@echo "... src/frame_processor.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
	@echo "... src/simple_yaml.o"
	@echo "... src/simple_yaml.i"
	@echo "... src/simple_yaml.s"
	@echo "... src/tensorrt_yolo_detector.o"
	@echo "... src/tensorrt_yolo_detector.i"
	@echo "... src/tensorrt_yolo_detector.s"
	@echo "... src/yolo_detector.o"
	@echo "... src/yolo_detector.i"
	@echo "... src/yolo_detector.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

