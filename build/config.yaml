# 可工作的配置文件 - 使用本地测试或可用流

# 流配置
stream:
  # 选项1: 使用本地测试视频文件
  input_stream: /home/<USER>/d.mp4
  
  # 选项3: 如果有可用的RTMP流，取消注释下面的行
  # input_stream: "rtmp://36.134.81.231:19403/live/your_stream_key"
  
  # 输出流地址
  output_stream: rtmp://36.134.81.231:19403/live/1945365858288603132
  
  # 固定输出分辨率 - 临时降低以测试网络
  output_width: 960
  output_height: 720
  
  # 帧率 (设置为25fps以匹配推流速度)
  fps: 0
  
  # 是否保持宽高比
  maintain_aspect_ratio: true
  
  # 编码配置 - 低延时设置
  encoding:
    # 关键帧间隔 (秒) - 稳定画质
    keyframe_interval: 2
    
    # 是否强制关键帧
    force_keyframes: true
    
    # 编码预设 - 优先画质
    preset: fast
    
    # H.264配置文件
    profile: baseline
    
    # H.264级别
    level: 3.1
    
    # 零延迟模式
    zero_latency: true
    
    # 参考帧数量
    refs: 1
    
    # B帧数量
    bframes: 0

# 处理器配置 - 低延时优化
processor:
  # 处理线程数
  num_threads: 4
  
  # 输入队列大小 - 进一步减小以降低延时
  input_queue_size: 3

  # 输出队列大小 - 进一步减小以降低延时
  output_queue_size: 3
  
  # 检测器配置列表
  detectors:
    # TensorRT车牌检测器
    detector_0:
      type: trt
      model_path: /home/<USER>/ed2/Edge-SDK-1.2/car/car.engine
      config_path: ""
      names_path: ""
      confidence_threshold: 0.5
      nms_threshold: 0.4

# 应用配置
app:
  # 是否启用目标检测
  enable_detection: true
  
  # 是否显示检测信息
  show_detection_info: true
