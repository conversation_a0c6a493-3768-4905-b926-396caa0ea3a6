# 视频流目标检测系统配置文件 - 包含关键帧设置防止黑屏

# 流配置
stream:
  # 输入流地址 (RTMP/HTTP/本地文件)
  input_stream: "rtmp://36.134.81.231:19403/live/8UUDMCT00AX6S7_165-0-7"
  
  # 输出流地址
  output_stream: "rtmp://36.134.81.231:19403/live/22222"
  
  # 固定输出分辨率
  output_width: 960
  output_height: 720
  
  # 帧率 (0表示自动检测)
  fps: 30
  
  # 是否保持宽高比 (true: 保持比例并添加黑边, false: 直接拉伸)
  maintain_aspect_ratio: false
  
  # 编码配置 - 关键帧设置防止黑屏
  encoding:
    # 关键帧间隔 (秒) - 每秒插入一个关键帧
    keyframe_interval: 1
    
    # 是否强制关键帧 - 防止播放器黑屏
    force_keyframes: true
    
    # 编码预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
    preset: "ultrafast"
    
    # H.264配置文件 (baseline, main, high) - baseline兼容性最好
    profile: "baseline"
    
    # H.264级别 (3.0, 3.1, 4.0, 4.1等)
    level: "3.1"
    
    # 零延迟模式 - 适用于实时流
    zero_latency: true
    
    # 参考帧数量 (1-16) - 较少的参考帧减少延迟
    refs: 1
    
    # B帧数量 (0-16) - 0表示禁用B帧，减少延迟
    bframes: 0

# 处理器配置
processor:
  # 处理线程数 (建议设置为CPU核心数)
  num_threads: 4
  
  # 输入队列大小 (帧缓冲)
  input_queue_size: 200
  
  # 输出队列大小 (结果缓冲)
  output_queue_size: 100
  
  # 检测器配置列表
  detectors:
    # 测试检测器
    detector_0:
      type: "test"
      model_path: ""
      config_path: ""
      names_path: ""
      confidence_threshold: 0.5
      nms_threshold: 0.4

# 应用配置
app:
  # 是否启用目标检测
  enable_detection: true
  
  # 是否显示检测信息
  show_detection_info: true
