cmake_minimum_required(VERSION 3.20)
project(rtmp_test)

set(CMAKE_CXX_STANDARD 20)

message("")
message("Operation system is ${CMAKE_SYSTEM}")
if (CMAKE_SYSTEM_NAME MATCHES "Linux")
    message("Current platform: Linux")
    link_directories(/usr/local/lib)
    link_directories(/usr/local/tensorrt-yolo/lib)
    include_directories(/usr/local/include)
    include_directories( /usr/include/opencv4/)
    include_directories( /usr/local/tensorrt-yolo/include/)
    message(STATUS "Load link directories from /usr/local/lib")
    message(STATUS "Load include directories from /usr/local/include")
elseif (CMAKE_SYSTEM_NAME MATCHES "Darwin")
    message("Current platform: Darwin")
    link_directories(/opt/homebrew/lib)
    include_directories(/opt/homebrew/include)
    message(STATUS "Load link directories from /opt/homebrew/lib")
    message(STATUS "Load include directories from /opt/homebrew/include")
    SET(OPENSSL_ROOT_DIR /opt/homebrew/Cellar/openssl@1.1/1.1.1l)
    SET(OPENSSL_INCLUDE_DIR /opt/homebrew/Cellar/openssl@1.1/1.1.1l/include)
else ()
    message(FATAL_ERROR "Platform ${CMAKE_SYSTEM_NAME} is not support for this project")
endif ()

find_package(OpenCV REQUIRED)
include_directories(${OpenCV_INCLUDE_DIRS})

# 添加头文件目录
include_directories(include)

# 查找线程库
find_package(Threads REQUIRED)

# 查找FFmpeg库
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED
    libavformat
    libavcodec
    libavutil
    libswscale
)
include_directories(${FFMPEG_INCLUDE_DIRS})
link_directories(${FFMPEG_LIBRARY_DIRS})

if (CMAKE_BUILD_TYPE STREQUAL Debug)
    ADD_DEFINITIONS(-DDEBUG)
    message(STATUS "CMake Build Type: Debug")
    message("")
elseif (CMAKE_BUILD_TYPE STREQUAL Release)
    message(STATUS "CMake Build Type: Release")
    message("")
endif ()

# 主程序
add_executable(${PROJECT_NAME}
    src/main.cc
    src/detector_base.cc
    src/frame_processor.cc
    src/yolo_detector.cc
    src/tensorrt_yolo_detector.cc
    src/detection_config.cc
    src/simple_yaml.cc
    src/ffmpeg_reader.cc
)
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBS}
    Threads::Threads
    ${FFMPEG_LIBRARIES}
    trtyolo
)

# 测试程序 - 暂时注释掉
# add_executable(test_detection
#     tests/test_detection.cc
#     src/detector_base.cc
#     src/frame_processor.cc
#     src/yolo_detector.cc
#     src/tensorrt_yolo_detector.cc
#     src/detection_config.cc
#     src/simple_yaml.cc
# )
# target_link_libraries(test_detection
#     ${OpenCV_LIBS}
#     Threads::Threads
#     ${FFMPEG_LIBRARIES}
#     trtyolo
# )

# 其他工具和测试程序 - 暂时注释掉
# 可以稍后根据需要添加